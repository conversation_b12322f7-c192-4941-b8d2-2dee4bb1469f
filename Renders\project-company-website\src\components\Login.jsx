import { useState } from 'react'
import { <PERSON>, useNavigate } from 'react-router-dom'
import { Lock, Mail, User, Eye, EyeOff } from 'lucide-react'

const Login = () => {
  const [isLogin, setIsLogin] = useState(true)
  const [showPassword, setShowPassword] = useState(false)
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    name: '',
    confirmPassword: ''
  })
  const navigate = useNavigate()

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    
    // Simple demo authentication
    if (isLogin) {
      // Check if admin credentials
      if (formData.email === '<EMAIL>' && formData.password === 'admin123') {
        navigate('/admin')
      } else {
        // Regular user login
        navigate('/user')
      }
    } else {
      // Registration - redirect to user dashboard
      navigate('/user')
    }
  }

  return (
    <div className="login-page">
      {/* Navigation */}
      <nav className="navbar">
        <div className="nav-container">
          <Link to="/" className="logo">RENDER SOLUTIONS</Link>
          <ul className="nav-links">
            <li><Link to="/">Home</Link></li>
            <li><Link to="/user">Dashboard</Link></li>
            <li><Link to="/submit-project">Submit Project</Link></li>
          </ul>
        </div>
      </nav>

      <div className="login-container">
        <div className="container">
          <div className="login-wrapper">
            <div className="login-card card">
              <div className="login-header">
                <h2 className="login-title">
                  {isLogin ? 'Welcome Back' : 'Create Account'}
                </h2>
                <p className="login-subtitle">
                  {isLogin 
                    ? 'Sign in to access your project dashboard' 
                    : 'Join our community of innovators'
                  }
                </p>
              </div>

              <form onSubmit={handleSubmit} className="login-form">
                {!isLogin && (
                  <div className="form-group">
                    <label className="form-label">
                      <User size={18} />
                      Full Name
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className="form-input"
                      placeholder="Enter your full name"
                      required={!isLogin}
                    />
                  </div>
                )}

                <div className="form-group">
                  <label className="form-label">
                    <Mail size={18} />
                    Email Address
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className="form-input"
                    placeholder="Enter your email"
                    required
                  />
                </div>

                <div className="form-group">
                  <label className="form-label">
                    <Lock size={18} />
                    Password
                  </label>
                  <div className="password-input-wrapper">
                    <input
                      type={showPassword ? 'text' : 'password'}
                      name="password"
                      value={formData.password}
                      onChange={handleInputChange}
                      className="form-input"
                      placeholder="Enter your password"
                      required
                    />
                    <button
                      type="button"
                      className="password-toggle"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                    </button>
                  </div>
                </div>

                {!isLogin && (
                  <div className="form-group">
                    <label className="form-label">
                      <Lock size={18} />
                      Confirm Password
                    </label>
                    <input
                      type="password"
                      name="confirmPassword"
                      value={formData.confirmPassword}
                      onChange={handleInputChange}
                      className="form-input"
                      placeholder="Confirm your password"
                      required={!isLogin}
                    />
                  </div>
                )}

                <button type="submit" className="btn btn-primary login-button">
                  {isLogin ? 'Sign In' : 'Create Account'}
                </button>
              </form>

              <div className="login-footer">
                <p>
                  {isLogin ? "Don't have an account? " : "Already have an account? "}
                  <button
                    type="button"
                    className="link-button"
                    onClick={() => setIsLogin(!isLogin)}
                  >
                    {isLogin ? 'Sign up' : 'Sign in'}
                  </button>
                </p>
              </div>

              {isLogin && (
                <div className="demo-credentials">
                  <h4>Demo Credentials:</h4>
                  <div className="demo-info">
                    <div className="demo-item">
                      <strong>Admin Access:</strong>
                      <p>Email: <EMAIL></p>
                      <p>Password: admin123</p>
                    </div>
                    <div className="demo-item">
                      <strong>User Access:</strong>
                      <p>Email: <EMAIL></p>
                      <p>Password: user123</p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="login-features">
              <h3>Why Join RENDER SOLUTIONS?</h3>
              <div className="feature-list">
                <div className="feature-item">
                  <div className="feature-icon">💡</div>
                  <div className="feature-content">
                    <h4>Share Your Ideas</h4>
                    <p>Submit innovative project concepts and get expert feedback</p>
                  </div>
                </div>
                <div className="feature-item">
                  <div className="feature-icon">🚀</div>
                  <div className="feature-content">
                    <h4>Bring Ideas to Life</h4>
                    <p>Work with our expert team to develop your projects</p>
                  </div>
                </div>
                <div className="feature-item">
                  <div className="feature-icon">🤝</div>
                  <div className="feature-content">
                    <h4>Collaborate</h4>
                    <p>Connect with other innovators and industry experts</p>
                  </div>
                </div>
                <div className="feature-item">
                  <div className="feature-icon">📈</div>
                  <div className="feature-content">
                    <h4>Track Progress</h4>
                    <p>Monitor your project development in real-time</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Login
