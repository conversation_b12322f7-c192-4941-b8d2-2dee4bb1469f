import { Link } from 'react-router-dom'
import { Lightbulb, Users, Target, ArrowRight, Star, CheckCircle, Sparkles, ChevronUp } from 'lucide-react'
import { useEffect, useState } from 'react'

const HomePage = () => {
  const [isVisible, setIsVisible] = useState(false)
  const [showScrollTop, setShowScrollTop] = useState(false)

  useEffect(() => {
    setIsVisible(true)

    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 300)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  return (
    <div className="homepage">
      {/* Floating Particles */}
      <div className="floating-particles">
        {[...Array(9)].map((_, i) => (
          <div key={i} className="particle"></div>
        ))}
      </div>

      {/* Navigation */}
      <nav className="navbar">
        <div className="nav-container">
          <Link to="/" className="logo">
            <Sparkles size={24} style={{marginRight: '0.5rem'}} />
            RENDER SOLUTIONS
          </Link>
          <ul className="nav-links">
            <li><Link to="/">Home</Link></li>
            <li><Link to="/submit-project">Submit Project</Link></li>
            <li><Link to="/login">Login</Link></li>
          </ul>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="hero">
        <div className="container">
          <div className="hero-content text-center">
            <h1 className={`hero-title ${isVisible ? 'animate-in' : ''}`}>
              Transform Your <span className="text-gradient">Ideas</span> Into Reality
            </h1>
            <p className={`hero-subtitle ${isVisible ? 'animate-in' : ''}`}>
              Join our innovative project company where creativity meets execution.
              Submit your project ideas, collaborate with experts, and bring your vision to life.
            </p>
            <div className={`hero-buttons ${isVisible ? 'animate-in' : ''}`}>
              <Link to="/submit-project" className="btn btn-primary btn-pulse">
                <Lightbulb size={20} />
                Submit Your Project
                <ArrowRight size={20} />
              </Link>
              <Link to="/user" className="btn btn-secondary">
                <Target size={20} />
                Browse Projects
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="features section">
        <div className="container">
          <h2 className="section-title text-center mb-4 animate-on-scroll">
            Why Choose <span className="text-gradient">RENDER SOLUTIONS</span>?
          </h2>
          <div className="grid grid-3">
            <div className="feature-card card card-animate text-center hover-lift">
              <div className="feature-icon icon-float">
                <div className="icon-bg">
                  <Lightbulb size={48} className="text-primary" />
                </div>
              </div>
              <h3 className="feature-title">Innovative Ideas</h3>
              <p className="feature-description">
                Share your creative project ideas and get expert feedback to refine and improve them.
              </p>
              <div className="feature-number">01</div>
            </div>
            <div className="feature-card card card-animate text-center hover-lift">
              <div className="feature-icon icon-float">
                <div className="icon-bg">
                  <Users size={48} className="text-primary" />
                </div>
              </div>
              <h3 className="feature-title">Expert Team</h3>
              <p className="feature-description">
                Work with our experienced team of developers, designers, and project managers.
              </p>
              <div className="feature-number">02</div>
            </div>
            <div className="feature-card card card-animate text-center hover-lift">
              <div className="feature-icon icon-float">
                <div className="icon-bg">
                  <Target size={48} className="text-primary" />
                </div>
              </div>
              <h3 className="feature-title">Goal Achievement</h3>
              <p className="feature-description">
                From concept to completion, we ensure your project reaches its full potential.
              </p>
              <div className="feature-number">03</div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="stats section">
        <div className="container">
          <div className="grid grid-3 text-center">
            <div className="stat-item card-animate hover-lift">
              <div className="stat-icon">
                <CheckCircle size={40} className="text-primary" />
              </div>
              <div className="stat-number counter" data-target="500">0+</div>
              <div className="stat-label">Projects Completed</div>
              <div className="stat-bg"></div>
            </div>
            <div className="stat-item card-animate hover-lift">
              <div className="stat-icon">
                <Users size={40} className="text-primary" />
              </div>
              <div className="stat-number counter" data-target="1000">0+</div>
              <div className="stat-label">Happy Clients</div>
              <div className="stat-bg"></div>
            </div>
            <div className="stat-item card-animate hover-lift">
              <div className="stat-icon">
                <Star size={40} className="text-primary" />
              </div>
              <div className="stat-number counter" data-target="50">0+</div>
              <div className="stat-label">Expert Team Members</div>
              <div className="stat-bg"></div>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="how-it-works section">
        <div className="container">
          <h2 className="section-title text-center mb-4 animate-on-scroll">
            How It <span className="text-gradient">Works</span>
          </h2>
          <div className="steps-timeline">
            <div className="step-card card card-animate text-center hover-lift">
              <div className="step-number pulse-ring">
                <span>1</span>
              </div>
              <div className="step-icon">
                <Lightbulb size={32} className="text-primary" />
              </div>
              <h3 className="step-title">Submit Your Idea</h3>
              <p className="step-description">
                Share your project concept with detailed requirements and goals.
              </p>
              <div className="step-arrow">→</div>
            </div>
            <div className="step-card card card-animate text-center hover-lift">
              <div className="step-number pulse-ring">
                <span>2</span>
              </div>
              <div className="step-icon">
                <Users size={32} className="text-primary" />
              </div>
              <h3 className="step-title">Expert Review</h3>
              <p className="step-description">
                Our team reviews and provides feedback to enhance your project.
              </p>
              <div className="step-arrow">→</div>
            </div>
            <div className="step-card card card-animate text-center hover-lift">
              <div className="step-number pulse-ring">
                <span>3</span>
              </div>
              <div className="step-icon">
                <Target size={32} className="text-primary" />
              </div>
              <h3 className="step-title">Bring to Life</h3>
              <p className="step-description">
                We execute your project with precision and deliver exceptional results.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta section">
        <div className="container">
          <div className="cta-content card text-center">
            <h2 className="cta-title">Ready to Start Your Project?</h2>
            <p className="cta-description">
              Join thousands of innovators who have transformed their ideas into successful projects with RENDER SOLUTIONS.
            </p>
            <Link to="/submit-project" className="btn btn-primary">
              Get Started Today <ArrowRight size={20} />
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer">
        <div className="container">
          <div className="footer-content text-center">
            <div className="footer-logo">RENDER SOLUTIONS</div>
            <p className="footer-text">
              Transforming ideas into reality, one project at a time.
            </p>
            <div className="footer-links">
              <Link to="/">Home</Link>
              <Link to="/submit-project">Submit Project</Link>
              <Link to="/login">Login</Link>
            </div>
            <p className="footer-copyright">
              © 2024 RENDER SOLUTIONS. All rights reserved.
            </p>
          </div>
        </div>
      </footer>

      {/* Scroll to Top Button */}
      <button
        className={`scroll-to-top ${showScrollTop ? 'visible' : ''}`}
        onClick={scrollToTop}
        aria-label="Scroll to top"
      >
        <ChevronUp size={24} />
      </button>
    </div>
  )
}

export default HomePage
