/* Modern CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Viewport optimizations */
html, body, #root {
  width: 100%;
  height: 100%;
  overflow-x: hidden;
}

html {
  scroll-behavior: smooth;
  height: 100%;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: #333;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  height: 100%;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
}

#root {
  height: 100%;
  min-height: 100vh;
}

.App {
  min-height: 100vh;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
}

/* Animated Background */
.App::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
  animation: backgroundFloat 20s ease-in-out infinite;
  z-index: -1;
}

@keyframes backgroundFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-30px) rotate(1deg); }
  66% { transform: translateY(15px) rotate(-1deg); }
}

/* Floating particles animation */
.floating-particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.particle:nth-child(1) { left: 10%; animation-delay: 0s; }
.particle:nth-child(2) { left: 20%; animation-delay: 1s; }
.particle:nth-child(3) { left: 30%; animation-delay: 2s; }
.particle:nth-child(4) { left: 40%; animation-delay: 3s; }
.particle:nth-child(5) { left: 50%; animation-delay: 4s; }
.particle:nth-child(6) { left: 60%; animation-delay: 5s; }
.particle:nth-child(7) { left: 70%; animation-delay: 0.5s; }
.particle:nth-child(8) { left: 80%; animation-delay: 1.5s; }
.particle:nth-child(9) { left: 90%; animation-delay: 2.5s; }

@keyframes float {
  0%, 100% { transform: translateY(100vh) scale(0); }
  10% { transform: translateY(90vh) scale(1); }
  90% { transform: translateY(-10vh) scale(1); }
  100% { transform: translateY(-10vh) scale(0); }
}

/* Navigation Styles */
.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  padding: 1rem 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  animation: slideDown 0.6s ease-out;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: 1.8rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
}

.logo::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: width 0.3s ease;
}

.logo:hover::after {
  width: 100%;
}

.nav-links {
  display: flex;
  gap: 2rem;
  list-style: none;
}

.nav-links li {
  position: relative;
}

.nav-links a {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.nav-links a::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: left 0.3s ease;
  z-index: -1;
}

.nav-links a:hover {
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.nav-links a:hover::before {
  left: 0;
}

/* Button Styles */
.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  cursor: pointer;
  font-size: 1rem;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4c93 100%);
}

.btn-primary:active {
  transform: translateY(-1px) scale(0.98);
}

.btn-secondary {
  background: transparent;
  color: #667eea;
  border: 2px solid #667eea;
  position: relative;
}

.btn-secondary::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: width 0.3s ease;
  z-index: -1;
}

.btn-secondary:hover {
  color: white;
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.btn-secondary:hover::after {
  width: 100%;
}

.btn-danger {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.btn-danger:hover {
  background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 15px 35px rgba(231, 76, 60, 0.4);
}

/* Pulse animation for important buttons */
.btn-pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
  }
}

/* Card Styles */
.card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2rem;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.card:hover::before {
  transform: translateX(0);
}

.card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.3);
}

/* Animated card entrance */
.card-animate {
  opacity: 0;
  transform: translateY(30px);
  animation: cardSlideUp 0.6s ease forwards;
}

@keyframes cardSlideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Staggered animation delays */
.card-animate:nth-child(1) { animation-delay: 0.1s; }
.card-animate:nth-child(2) { animation-delay: 0.2s; }
.card-animate:nth-child(3) { animation-delay: 0.3s; }
.card-animate:nth-child(4) { animation-delay: 0.4s; }
.card-animate:nth-child(5) { animation-delay: 0.5s; }
.card-animate:nth-child(6) { animation-delay: 0.6s; }

/* Container Styles */
.container {
  max-width: 1400px;
  width: 100%;
  margin: 0 auto;
  padding: 0 2rem;
}

.section {
  padding: 4rem 0;
  width: 100%;
}

/* Full width containers for dashboards */
.dashboard-container .container,
.admin-container .container {
  max-width: 100%;
  padding: 0 2rem;
}

/* Responsive container adjustments */
@media (min-width: 1600px) {
  .container {
    max-width: 1600px;
  }

  .dashboard-container .container,
  .admin-container .container {
    max-width: 100%;
    padding: 0 3rem;
  }
}

/* Grid Styles */
.grid {
  display: grid;
  gap: 2rem;
  width: 100%;
}

.grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

.grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-4 {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

/* Responsive grid adjustments */
@media (min-width: 1200px) {
  .grid-3 {
    grid-template-columns: repeat(3, 1fr);
  }

  .grid-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1600px) {
  .grid-3 {
    grid-template-columns: repeat(4, 1fr);
  }

  .grid-4 {
    grid-template-columns: repeat(5, 1fr);
  }

  .projects-grid {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  }
}

/* Form Styles */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #333;
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: translateY(-2px);
}

.form-input:hover {
  border-color: #a8b3f0;
  transform: translateY(-1px);
}

.form-textarea {
  min-height: 120px;
  resize: vertical;
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.text-primary {
  color: #667eea;
}

.text-muted {
  color: #6c757d;
}

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

/* Homepage Layout */
.homepage {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.homepage .navbar {
  flex-shrink: 0;
}

/* Homepage Specific Styles */
.hero {
  background:
    linear-gradient(135deg, #667eea 0%, #764ba2 100%),
    radial-gradient(circle at 30% 70%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  color: white;
  min-height: calc(100vh - 80px);
  display: flex;
  align-items: center;
  text-align: center;
  position: relative;
  overflow: hidden;
  flex: 1;
}

.hero::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background:
    radial-gradient(circle, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: heroPattern 20s linear infinite;
  opacity: 0.3;
}

@keyframes heroPattern {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

.hero-content {
  position: relative;
  z-index: 2;
}

.hero-title {
  font-size: 4rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  line-height: 1.1;
  background: linear-gradient(45deg, #ffffff, #e0e7ff, #ffffff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
  from {
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
  }
  to {
    text-shadow: 0 0 30px rgba(255, 255, 255, 0.8);
  }
}

.hero-title span {
  display: inline-block;
  animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.hero-subtitle {
  font-size: 1.4rem;
  margin-bottom: 3rem;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  opacity: 0.95;
  line-height: 1.6;
  animation: fadeInUp 1s ease-out 0.5s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 0.95;
    transform: translateY(0);
  }
}

.hero-buttons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
  animation: fadeInUp 1s ease-out 1s both;
}

.hero-buttons .btn {
  font-size: 1.1rem;
  padding: 1rem 2rem;
  border-radius: 50px;
}

.hero-buttons .btn-primary {
  animation: pulse 2s infinite;
}

/* Text gradient effect */
.text-gradient {
  background: linear-gradient(45deg, #667eea, #764ba2, #667eea);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Animation classes */
.animate-in {
  animation: slideInUp 0.8s ease-out forwards;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-on-scroll {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.animate-on-scroll.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Feature card enhancements */
.feature-card {
  position: relative;
  overflow: hidden;
}

.feature-icon {
  margin-bottom: 2rem;
  position: relative;
}

.icon-bg {
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  position: relative;
  transition: all 0.3s ease;
}

.icon-bg::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #667eea, #764ba2);
  border-radius: 50%;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.feature-card:hover .icon-bg::before {
  opacity: 1;
}

.feature-card:hover .icon-bg {
  background: white;
  transform: scale(1.1);
}

.icon-float {
  animation: iconFloat 3s ease-in-out infinite;
}

@keyframes iconFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.feature-number {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 0.875rem;
  opacity: 0.8;
}

.hover-lift {
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.hover-lift:hover {
  transform: translateY(-15px) scale(1.03);
}

/* Enhanced section title */
.section-title {
  font-size: 3rem;
  font-weight: 800;
  color: #333;
  position: relative;
  margin-bottom: 3rem;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
}

.feature-icon {
  margin-bottom: 1.5rem;
}

.feature-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #333;
}

.feature-description {
  color: #6c757d;
  line-height: 1.6;
}

.stats {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
  overflow: hidden;
}

.stats::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(118, 75, 162, 0.05) 0%, transparent 50%);
}

.stat-item {
  position: relative;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.stat-icon {
  margin-bottom: 1rem;
  opacity: 0.8;
}

.stat-number {
  font-size: 3.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
  position: relative;
}

.stat-label {
  font-size: 1.1rem;
  color: #6c757d;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-bg {
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.stat-item:hover .stat-bg {
  transform: scale(3);
  opacity: 0.3;
}

/* Counter animation */
.counter {
  animation: countUp 2s ease-out forwards;
}

@keyframes countUp {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Steps timeline */
.steps-timeline {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  position: relative;
}

.step-card {
  position: relative;
  padding: 3rem 2rem;
}

.step-number {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: 800;
  margin: 0 auto 1.5rem;
  position: relative;
  z-index: 2;
}

.pulse-ring {
  position: relative;
}

.pulse-ring::before {
  content: '';
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border: 2px solid rgba(102, 126, 234, 0.3);
  border-radius: 50%;
  animation: pulse-ring 2s ease-out infinite;
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

.step-icon {
  margin-bottom: 1.5rem;
  opacity: 0.8;
}

.step-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #333;
}

.step-description {
  color: #6c757d;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.step-arrow {
  position: absolute;
  top: 50%;
  right: -1rem;
  font-size: 2rem;
  color: #667eea;
  transform: translateY(-50%);
  opacity: 0.6;
  animation: arrowFloat 2s ease-in-out infinite;
}

@keyframes arrowFloat {
  0%, 100% { transform: translateY(-50%) translateX(0); }
  50% { transform: translateY(-50%) translateX(5px); }
}

.step-card:last-child .step-arrow {
  display: none;
}

.step-number {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 auto 1.5rem;
}

.step-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #333;
}

.step-description {
  color: #6c757d;
  line-height: 1.6;
}

.cta {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.cta-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #333;
}

.cta-description {
  font-size: 1.1rem;
  color: #6c757d;
  margin-bottom: 2rem;
}

.footer {
  background: #2c3e50;
  color: white;
  padding: 3rem 0 1rem;
}

.footer-logo {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #667eea;
}

.footer-text {
  margin-bottom: 2rem;
  opacity: 0.8;
}

.footer-links {
  display: flex;
  gap: 2rem;
  justify-content: center;
  margin-bottom: 2rem;
}

.footer-links a {
  color: white;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: #667eea;
}

.footer-copyright {
  opacity: 0.6;
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-container {
    flex-direction: column;
    gap: 1rem;
  }

  .nav-links {
    gap: 1rem;
  }

  .container {
    padding: 0 1rem;
  }

  .section {
    padding: 2rem 0;
  }

  .hero {
    padding: 4rem 0;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .footer-links {
    flex-direction: column;
    gap: 1rem;
  }
}

/* Dashboard Layout */
.user-dashboard {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}

.user-dashboard .navbar {
  flex-shrink: 0;
}

/* Dashboard Styles */
.dashboard-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
  padding: 2rem 0;
  min-height: calc(100vh - 80px);
}

.dashboard-header {
  text-align: center;
  margin-bottom: 4rem;
  position: relative;
}

.dashboard-title {
  font-size: 3rem;
  font-weight: 800;
  color: #333;
  margin-bottom: 1rem;
  position: relative;
}

.dashboard-subtitle {
  font-size: 1.2rem;
  color: #6c757d;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.header-stats {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.stat-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border-radius: 25px;
  font-weight: 600;
  font-size: 0.875rem;
  border: 1px solid rgba(102, 126, 234, 0.2);
  transition: all 0.3s ease;
}

.stat-badge:hover {
  background: rgba(102, 126, 234, 0.2);
  transform: translateY(-2px);
}

.dashboard-controls {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  padding: 1.5rem;
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.dashboard-controls .search-box,
.dashboard-controls .filter-box {
  flex: 1;
  min-width: 250px;
}

.results-count {
  color: #6c757d;
  font-weight: 600;
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 20px;
  border: 1px solid rgba(102, 126, 234, 0.2);
  white-space: nowrap;
}

.search-box, .filter-box {
  position: relative;
  flex: 1;
  min-width: 250px;
}

.search-icon, .filter-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
}

.search-input, .filter-select {
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 3rem;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.search-input:focus, .filter-select:focus {
  outline: none;
  border-color: #667eea;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  width: 100%;
  flex: 1;
  align-content: start;
}

/* Ensure projects fill the available space */
.dashboard-container .container {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1400px;
  width: 100%;
  margin: 0 auto;
  padding: 0 2rem;
}

.dashboard-container .container > * {
  flex-shrink: 0;
}

.dashboard-container .projects-grid {
  flex: 1;
}

.project-card {
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
}

.project-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
  transition: left 0.5s ease;
}

.project-card:hover::before {
  left: 100%;
}

.project-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(102, 126, 234, 0.1);
}

.project-card:hover .project-title {
  color: #667eea;
}

.project-card:hover .project-status {
  transform: scale(1.05);
}

.project-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 20px;
  z-index: -1;
}

.project-card:hover .project-glow {
  opacity: 1;
}

/* Enhanced project status badges */
.project-status {
  padding: 0.4rem 1rem;
  border-radius: 25px;
  font-size: 0.75rem;
  font-weight: 600;
  white-space: nowrap;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.project-status::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.project-status:hover::before {
  left: 100%;
}

.status-completed {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  color: #155724;
  box-shadow: 0 2px 10px rgba(21, 87, 36, 0.2);
}

.status-progress {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  color: #856404;
  box-shadow: 0 2px 10px rgba(133, 100, 4, 0.2);
}

.status-review {
  background: linear-gradient(135deg, #cce5ff 0%, #b3d9ff 100%);
  color: #004085;
  box-shadow: 0 2px 10px rgba(0, 64, 133, 0.2);
}

.status-planning {
  background: linear-gradient(135deg, #f8d7da 0%, #f1b0b7 100%);
  color: #721c24;
  box-shadow: 0 2px 10px rgba(114, 28, 36, 0.2);
}

/* Enhanced project tags */
.project-tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.3rem 0.8rem;
  background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%);
  color: #495057;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.project-tag:hover {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

/* Loading animation for project cards */
.projects-grid .card-animate {
  opacity: 0;
  transform: translateY(30px) scale(0.95);
  animation: projectCardSlideUp 0.6s ease forwards;
}

@keyframes projectCardSlideUp {
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Loading Spinner */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Scroll to top button */
.scroll-to-top {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  z-index: 1000;
  opacity: 0;
  transform: translateY(100px);
}

.scroll-to-top.visible {
  opacity: 1;
  transform: translateY(0);
}

.scroll-to-top:hover {
  transform: translateY(-5px) scale(1.1);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* Tooltip */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  z-index: 1000;
}

.tooltip:hover::after {
  opacity: 1;
}

/* Notification toast */
.toast {
  position: fixed;
  top: 2rem;
  right: 2rem;
  background: white;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border-left: 4px solid #667eea;
  z-index: 1000;
  transform: translateX(400px);
  transition: transform 0.3s ease;
}

.toast.show {
  transform: translateX(0);
}

.toast.success {
  border-left-color: #27ae60;
}

.toast.error {
  border-left-color: #e74c3c;
}

/* Skeleton loading */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.skeleton-text {
  height: 1rem;
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

.skeleton-title {
  height: 1.5rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  width: 70%;
}

/* Enhanced focus states */
*:focus-visible {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Selection styling */
::selection {
  background: rgba(102, 126, 234, 0.3);
  color: #333;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.project-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
  margin: 0;
  flex: 1;
}

.project-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
}

.status-completed {
  background: #d4edda;
  color: #155724;
}

.status-progress {
  background: #fff3cd;
  color: #856404;
}

.status-review {
  background: #cce5ff;
  color: #004085;
}

.status-planning {
  background: #f8d7da;
  color: #721c24;
}

.project-description {
  color: #6c757d;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.project-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6c757d;
  font-size: 0.875rem;
}

.project-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.project-tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  background: #e9ecef;
  color: #495057;
  border-radius: 15px;
  font-size: 0.75rem;
  font-weight: 500;
}

.project-actions {
  display: flex;
  gap: 1rem;
}

.no-results {
  text-align: center;
  padding: 4rem 2rem;
  color: #6c757d;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin: 2rem 0;
}

.no-results h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #333;
}

.no-results p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
}

.no-results .btn {
  margin-top: 1rem;
}

.no-results-icon {
  margin-bottom: 2rem;
  opacity: 0.6;
  animation: iconFloat 3s ease-in-out infinite;
}

/* Admin Dashboard Layout */
.admin-dashboard {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}

.admin-dashboard .navbar {
  flex-shrink: 0;
}

/* Admin Dashboard Styles */
.admin-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
  padding: 2rem 0;
  min-height: calc(100vh - 80px);
}

.admin-container .container {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1400px;
  width: 100%;
  margin: 0 auto;
  padding: 0 2rem;
}

.admin-header {
  text-align: center;
  margin-bottom: 3rem;
}

.admin-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 1rem;
}

.admin-subtitle {
  font-size: 1.1rem;
  color: #6c757d;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
  width: 100%;
}

@media (min-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 1.5rem;
}

.stat-icon {
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-card .stat-number {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.stat-card .stat-label {
  font-size: 0.875rem;
  color: #6c757d;
  font-weight: 500;
}

.admin-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  border-bottom: 2px solid #e1e5e9;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  border: none;
  background: transparent;
  color: #6c757d;
  font-weight: 500;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
}

.tab-button:hover {
  color: #667eea;
}

.tab-button.active {
  color: #667eea;
  border-bottom-color: #667eea;
}

.admin-controls {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.projects-table, .users-table {
  overflow-x: auto;
}

.table-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e1e5e9;
}

.table-header h3 {
  margin: 0;
  color: #333;
}

.table-content {
  padding: 0;
}

.table-content table {
  width: 100%;
  border-collapse: collapse;
}

.table-content th,
.table-content td {
  padding: 1rem 1.5rem;
  text-align: left;
  border-bottom: 1px solid #e1e5e9;
}

.table-content th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.status-badge, .priority-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.75rem;
  font-weight: 500;
}

.priority-high {
  background: #f8d7da;
  color: #721c24;
}

.priority-medium {
  background: #fff3cd;
  color: #856404;
}

.priority-low {
  background: #d4edda;
  color: #155724;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.btn-icon {
  padding: 0.5rem;
  border: none;
  background: #f8f9fa;
  color: #6c757d;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-icon:hover {
  background: #e9ecef;
  color: #333;
}

.btn-icon.btn-danger:hover {
  background: #e74c3c;
  color: white;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.activity-icon {
  flex-shrink: 0;
  margin-top: 0.25rem;
}

.activity-content p {
  margin: 0 0 0.25rem 0;
  color: #333;
}

.activity-time {
  font-size: 0.875rem;
  color: #6c757d;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.quick-actions .btn {
  justify-content: flex-start;
}

/* Login Page Styles */
.login-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-page .navbar {
  flex-shrink: 0;
}

.login-container {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 4rem 0;
  min-height: calc(100vh - 80px);
}

.login-wrapper {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  max-width: 1000px;
  margin: 0 auto;
}

.login-card {
  background: white;
  padding: 3rem;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-title {
  font-size: 2rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.5rem;
}

.login-subtitle {
  color: #6c757d;
  font-size: 1rem;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.login-form .form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #333;
}

.password-input-wrapper {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  border: none;
  background: transparent;
  color: #6c757d;
  cursor: pointer;
  padding: 0.25rem;
}

.login-button {
  width: 100%;
  padding: 1rem;
  font-size: 1.1rem;
  margin-top: 1rem;
}

.login-footer {
  text-align: center;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e1e5e9;
}

.link-button {
  background: none;
  border: none;
  color: #667eea;
  font-weight: 600;
  cursor: pointer;
  text-decoration: underline;
}

.demo-credentials {
  margin-top: 2rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.demo-credentials h4 {
  margin-bottom: 1rem;
  color: #333;
}

.demo-info {
  display: flex;
  gap: 2rem;
}

.demo-item {
  flex: 1;
}

.demo-item strong {
  display: block;
  margin-bottom: 0.5rem;
  color: #667eea;
}

.demo-item p {
  margin: 0.25rem 0;
  font-size: 0.875rem;
  color: #6c757d;
}

.login-features {
  color: white;
  padding: 2rem;
}

.login-features h3 {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  text-align: center;
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.feature-item {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.feature-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.feature-content h4 {
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.feature-content p {
  opacity: 0.9;
  line-height: 1.5;
}

/* Project Submission Layout */
.project-submission {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}

.project-submission .navbar {
  flex-shrink: 0;
}

/* Project Submission Styles */
.submission-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
  padding: 2rem 0;
  min-height: calc(100vh - 80px);
}

.submission-header {
  margin-bottom: 3rem;
}

.submission-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 1rem;
}

.submission-subtitle {
  font-size: 1.1rem;
  color: #6c757d;
  max-width: 600px;
  margin: 0 auto;
}

.submission-wrapper {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 3rem;
  max-width: 1200px;
  margin: 0 auto;
}

.submission-form {
  padding: 2.5rem;
}

.form-section {
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #e1e5e9;
}

.form-section:last-of-type {
  border-bottom: none;
  margin-bottom: 2rem;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 2rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding-top: 2rem;
  border-top: 1px solid #e1e5e9;
}

.submit-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  font-size: 1.1rem;
}

.submission-sidebar {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.sidebar-card {
  padding: 2rem;
}

.sidebar-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1.5rem;
}

.guidelines-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.guideline-item {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.guideline-item h4 {
  margin-bottom: 0.5rem;
  color: #333;
}

.guideline-item p {
  color: #6c757d;
  line-height: 1.5;
}

.process-steps {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.process-step {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.process-step .step-number {
  width: 30px;
  height: 30px;
  background: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.process-step p {
  color: #6c757d;
  line-height: 1.5;
  margin: 0;
}

/* Success Page Styles */
.submission-success {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.success-container {
  padding: 4rem 0;
  display: flex;
  align-items: center;
  min-height: calc(100vh - 80px);
}

.success-card {
  max-width: 600px;
  margin: 0 auto;
  padding: 3rem;
  background: white;
}

.success-icon {
  margin-bottom: 2rem;
}

.success-title {
  font-size: 2rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 1rem;
}

.success-message {
  color: #6c757d;
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.success-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Project Details Layout */
.project-details {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}

.project-details .navbar {
  flex-shrink: 0;
}

/* Project Details Styles */
.details-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
  padding: 2rem 0;
  min-height: calc(100vh - 80px);
}

.details-container .container {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1400px;
  width: 100%;
  margin: 0 auto;
  padding: 0 2rem;
}

.details-header {
  margin-bottom: 2rem;
}

.back-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  margin-bottom: 1.5rem;
  transition: color 0.3s ease;
}

.back-button:hover {
  color: #5a6fd8;
}

.project-header-info {
  margin-bottom: 2rem;
}

.project-header-info .project-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 1rem;
}

.project-header-info .project-meta {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.project-priority {
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.875rem;
  font-weight: 500;
}

.details-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  border-bottom: 2px solid #e1e5e9;
}

.details-tabs .tab-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  border: none;
  background: transparent;
  color: #6c757d;
  font-weight: 500;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
}

.details-tabs .tab-button:hover {
  color: #667eea;
}

.details-tabs .tab-button.active {
  color: #667eea;
  border-bottom-color: #667eea;
}

.overview-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.sidebar-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1.5rem;
}

.goals-list, .requirements-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.goal-item, .requirement-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin-bottom: 1rem;
  line-height: 1.6;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.info-item {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.info-item > div {
  flex: 1;
}

.info-label {
  display: block;
  font-size: 0.875rem;
  color: #6c757d;
  margin-bottom: 0.25rem;
}

.info-value {
  display: block;
  font-weight: 600;
  color: #333;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.timeline {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  position: relative;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 20px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #e1e5e9;
}

.timeline-item {
  display: flex;
  gap: 1.5rem;
  position: relative;
}

.timeline-marker {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border: 2px solid #e1e5e9;
  flex-shrink: 0;
  z-index: 1;
}

.timeline-item.completed .timeline-marker {
  background: #d4edda;
  border-color: #27ae60;
  color: #27ae60;
}

.timeline-item.in-progress .timeline-marker {
  background: #fff3cd;
  border-color: #f39c12;
  color: #f39c12;
}

.timeline-item.pending .timeline-marker {
  background: #f8d7da;
  border-color: #e74c3c;
  color: #e74c3c;
}

.timeline-content {
  flex: 1;
  padding-bottom: 1rem;
}

.timeline-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.timeline-description {
  color: #6c757d;
  line-height: 1.6;
  margin-bottom: 0.5rem;
}

.timeline-date {
  font-size: 0.875rem;
  color: #6c757d;
  font-weight: 500;
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.team-member {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.member-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #667eea;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.member-name {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
}

.member-role {
  font-size: 0.875rem;
  color: #6c757d;
  margin: 0;
}

.updates-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.update-item {
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.update-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.update-author {
  font-weight: 600;
  color: #333;
}

.update-date {
  font-size: 0.875rem;
  color: #6c757d;
}

.update-message {
  color: #6c757d;
  line-height: 1.6;
  margin: 0;
}

/* Large screen optimizations */
@media (min-width: 1920px) {
  .container {
    max-width: 1800px;
  }

  .projects-grid {
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: 2.5rem;
  }

  .hero-title {
    font-size: 5rem;
  }

  .section-title {
    font-size: 3.5rem;
  }

  .dashboard-title {
    font-size: 3.5rem;
  }
}

/* Ultra-wide screen optimizations */
@media (min-width: 2560px) {
  .container {
    max-width: 2400px;
  }

  .projects-grid {
    grid-template-columns: repeat(auto-fit, minmax(420px, 1fr));
    gap: 3rem;
  }
}

/* Responsive Design for New Components */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .section-title {
    font-size: 2rem;
  }

  .dashboard-title {
    font-size: 2rem;
  }

  .header-stats {
    flex-direction: column;
    align-items: center;
  }

  .login-wrapper {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .submission-wrapper {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .overview-content {
    grid-template-columns: 1fr;
  }

  .demo-info {
    flex-direction: column;
    gap: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .projects-grid {
    grid-template-columns: 1fr;
  }

  .team-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-controls,
  .admin-controls {
    flex-direction: column;
  }

  .search-box,
  .filter-box {
    min-width: auto;
  }

  .steps-timeline {
    grid-template-columns: 1fr;
  }

  .step-arrow {
    display: none;
  }

  .nav-links {
    flex-direction: column;
    gap: 0.5rem;
  }

  .hero {
    padding: 4rem 0;
  }

  .floating-particles {
    display: none;
  }

  .scroll-to-top {
    bottom: 1rem;
    right: 1rem;
    width: 45px;
    height: 45px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .dashboard-title {
    font-size: 1.5rem;
  }

  .card {
    padding: 1.5rem;
  }

  .btn {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }

  .navbar {
    padding: 0.75rem 1rem;
  }

  .container {
    padding: 0 0.5rem;
  }
}
