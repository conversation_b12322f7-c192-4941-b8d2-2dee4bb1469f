import { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { 
  Send, 
  FileText, 
  Tag, 
  Calendar, 
  DollarSign, 
  Users, 
  Target,
  Lightbulb,
  CheckCircle
} from 'lucide-react'

const ProjectSubmission = () => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    budget: '',
    timeline: '',
    teamSize: '',
    goals: '',
    requirements: '',
    tags: '',
    priority: 'medium'
  })
  const [isSubmitted, setIsSubmitted] = useState(false)
  const navigate = useNavigate()

  const categories = [
    'Mobile Development',
    'Web Development',
    'AI/Machine Learning',
    'IoT',
    'Blockchain',
    'VR/AR',
    'Education',
    'Healthcare',
    'Sustainability',
    'E-commerce',
    'Gaming',
    'Other'
  ]

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    // Simulate form submission
    setIsSubmitted(true)
    
    // Redirect to user dashboard after 3 seconds
    setTimeout(() => {
      navigate('/user')
    }, 3000)
  }

  if (isSubmitted) {
    return (
      <div className="submission-success">
        <nav className="navbar">
          <div className="nav-container">
            <Link to="/" className="logo">RENDER SOLUTIONS</Link>
            <ul className="nav-links">
              <li><Link to="/">Home</Link></li>
              <li><Link to="/user">Dashboard</Link></li>
              <li><Link to="/submit-project">Submit Project</Link></li>
            </ul>
          </div>
        </nav>
        
        <div className="success-container">
          <div className="container">
            <div className="success-card card text-center">
              <div className="success-icon">
                <CheckCircle size={64} style={{color: '#27ae60'}} />
              </div>
              <h2 className="success-title">Project Submitted Successfully!</h2>
              <p className="success-message">
                Thank you for submitting your project idea. Our team will review it and get back to you within 2-3 business days.
              </p>
              <div className="success-actions">
                <Link to="/user" className="btn btn-primary">
                  View Dashboard
                </Link>
                <Link to="/submit-project" className="btn btn-secondary">
                  Submit Another Project
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="project-submission">
      {/* Navigation */}
      <nav className="navbar">
        <div className="nav-container">
          <Link to="/" className="logo">RENDER SOLUTIONS</Link>
          <ul className="nav-links">
            <li><Link to="/">Home</Link></li>
            <li><Link to="/user">Dashboard</Link></li>
            <li><Link to="/submit-project">Submit Project</Link></li>
            <li><Link to="/login">Login</Link></li>
          </ul>
        </div>
      </nav>

      <div className="submission-container">
        <div className="container">
          <div className="submission-header text-center">
            <h1 className="submission-title">Submit Your Project Idea</h1>
            <p className="submission-subtitle">
              Share your innovative concept with our expert team and bring your vision to life
            </p>
          </div>

          <div className="submission-wrapper">
            <div className="submission-form-container">
              <form onSubmit={handleSubmit} className="submission-form card">
                <div className="form-section">
                  <h3 className="section-title">
                    <Lightbulb size={24} />
                    Project Overview
                  </h3>
                  
                  <div className="form-group">
                    <label className="form-label">Project Title *</label>
                    <input
                      type="text"
                      name="title"
                      value={formData.title}
                      onChange={handleInputChange}
                      className="form-input"
                      placeholder="Enter a compelling project title"
                      required
                    />
                  </div>

                  <div className="form-group">
                    <label className="form-label">Project Description *</label>
                    <textarea
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      className="form-input form-textarea"
                      placeholder="Describe your project idea in detail..."
                      rows="5"
                      required
                    />
                  </div>

                  <div className="form-row">
                    <div className="form-group">
                      <label className="form-label">Category *</label>
                      <select
                        name="category"
                        value={formData.category}
                        onChange={handleInputChange}
                        className="form-input"
                        required
                      >
                        <option value="">Select a category</option>
                        {categories.map(category => (
                          <option key={category} value={category}>{category}</option>
                        ))}
                      </select>
                    </div>

                    <div className="form-group">
                      <label className="form-label">Priority Level</label>
                      <select
                        name="priority"
                        value={formData.priority}
                        onChange={handleInputChange}
                        className="form-input"
                      >
                        <option value="low">Low</option>
                        <option value="medium">Medium</option>
                        <option value="high">High</option>
                      </select>
                    </div>
                  </div>
                </div>

                <div className="form-section">
                  <h3 className="section-title">
                    <Target size={24} />
                    Project Details
                  </h3>

                  <div className="form-group">
                    <label className="form-label">Project Goals *</label>
                    <textarea
                      name="goals"
                      value={formData.goals}
                      onChange={handleInputChange}
                      className="form-input form-textarea"
                      placeholder="What do you want to achieve with this project?"
                      rows="3"
                      required
                    />
                  </div>

                  <div className="form-group">
                    <label className="form-label">Technical Requirements</label>
                    <textarea
                      name="requirements"
                      value={formData.requirements}
                      onChange={handleInputChange}
                      className="form-input form-textarea"
                      placeholder="List any specific technical requirements, platforms, or technologies"
                      rows="3"
                    />
                  </div>

                  <div className="form-row">
                    <div className="form-group">
                      <label className="form-label">
                        <DollarSign size={18} />
                        Estimated Budget
                      </label>
                      <select
                        name="budget"
                        value={formData.budget}
                        onChange={handleInputChange}
                        className="form-input"
                      >
                        <option value="">Select budget range</option>
                        <option value="under-5k">Under $5,000</option>
                        <option value="5k-15k">$5,000 - $15,000</option>
                        <option value="15k-50k">$15,000 - $50,000</option>
                        <option value="50k-100k">$50,000 - $100,000</option>
                        <option value="over-100k">Over $100,000</option>
                      </select>
                    </div>

                    <div className="form-group">
                      <label className="form-label">
                        <Calendar size={18} />
                        Timeline
                      </label>
                      <select
                        name="timeline"
                        value={formData.timeline}
                        onChange={handleInputChange}
                        className="form-input"
                      >
                        <option value="">Select timeline</option>
                        <option value="1-3-months">1-3 months</option>
                        <option value="3-6-months">3-6 months</option>
                        <option value="6-12-months">6-12 months</option>
                        <option value="over-1-year">Over 1 year</option>
                      </select>
                    </div>
                  </div>

                  <div className="form-row">
                    <div className="form-group">
                      <label className="form-label">
                        <Users size={18} />
                        Team Size Needed
                      </label>
                      <select
                        name="teamSize"
                        value={formData.teamSize}
                        onChange={handleInputChange}
                        className="form-input"
                      >
                        <option value="">Select team size</option>
                        <option value="1-2">1-2 people</option>
                        <option value="3-5">3-5 people</option>
                        <option value="6-10">6-10 people</option>
                        <option value="over-10">Over 10 people</option>
                      </select>
                    </div>

                    <div className="form-group">
                      <label className="form-label">
                        <Tag size={18} />
                        Tags
                      </label>
                      <input
                        type="text"
                        name="tags"
                        value={formData.tags}
                        onChange={handleInputChange}
                        className="form-input"
                        placeholder="e.g., React, AI, Mobile, API (comma separated)"
                      />
                    </div>
                  </div>
                </div>

                <div className="form-actions">
                  <button type="submit" className="btn btn-primary submit-button">
                    <Send size={20} />
                    Submit Project
                  </button>
                  <Link to="/user" className="btn btn-secondary">
                    Cancel
                  </Link>
                </div>
              </form>
            </div>

            <div className="submission-sidebar">
              <div className="sidebar-card card">
                <h3 className="sidebar-title">Submission Guidelines</h3>
                <div className="guidelines-list">
                  <div className="guideline-item">
                    <FileText size={20} className="text-primary" />
                    <div>
                      <h4>Be Detailed</h4>
                      <p>Provide comprehensive information about your project vision</p>
                    </div>
                  </div>
                  <div className="guideline-item">
                    <Target size={20} className="text-primary" />
                    <div>
                      <h4>Clear Goals</h4>
                      <p>Define specific, measurable objectives for your project</p>
                    </div>
                  </div>
                  <div className="guideline-item">
                    <Users size={20} className="text-primary" />
                    <div>
                      <h4>Team Requirements</h4>
                      <p>Specify the skills and team size needed</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="sidebar-card card">
                <h3 className="sidebar-title">What Happens Next?</h3>
                <div className="process-steps">
                  <div className="process-step">
                    <div className="step-number">1</div>
                    <p>We review your submission within 2-3 business days</p>
                  </div>
                  <div className="process-step">
                    <div className="step-number">2</div>
                    <p>Our team provides feedback and suggestions</p>
                  </div>
                  <div className="process-step">
                    <div className="step-number">3</div>
                    <p>We create a detailed project plan and timeline</p>
                  </div>
                  <div className="process-step">
                    <div className="step-number">4</div>
                    <p>Project development begins with regular updates</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProjectSubmission
