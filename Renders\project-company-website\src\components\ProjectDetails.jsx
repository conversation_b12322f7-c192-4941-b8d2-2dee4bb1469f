import { useState } from 'react'
import { Link, useParams } from 'react-router-dom'
import { 
  ArrowLeft, 
  Calendar, 
  User, 
  Tag, 
  Target, 
  DollarSign, 
  Users, 
  Clock,
  MessageCircle,
  FileText,
  CheckCircle,
  AlertCircle
} from 'lucide-react'

const ProjectDetails = () => {
  const { id } = useParams()
  const [activeTab, setActiveTab] = useState('overview')

  // Sample project data - in real app, this would be fetched based on ID
  const project = {
    id: parseInt(id),
    title: "E-commerce Mobile App",
    description: "A modern mobile application for online shopping with AI-powered product recommendations, seamless payment integration, and personalized user experience. The app will feature advanced search capabilities, real-time inventory management, and social shopping features.",
    category: "Mobile Development",
    status: "In Progress",
    priority: "High",
    submittedBy: "John Doe",
    submittedDate: "2024-01-15",
    lastUpdated: "2024-01-20",
    budget: "$15,000 - $50,000",
    timeline: "3-6 months",
    teamSize: "3-5 people",
    tags: ["React Native", "AI", "E-commerce", "Payment Gateway", "Mobile"],
    goals: [
      "Create an intuitive mobile shopping experience",
      "Implement AI-powered product recommendations",
      "Integrate secure payment processing",
      "Build real-time inventory management",
      "Develop social shopping features"
    ],
    requirements: [
      "Cross-platform mobile app (iOS and Android)",
      "AI/ML integration for recommendations",
      "Payment gateway integration (Stripe, PayPal)",
      "Real-time database synchronization",
      "Push notification system",
      "Admin dashboard for inventory management"
    ],
    progress: [
      {
        phase: "Planning & Design",
        status: "completed",
        date: "2024-01-15",
        description: "Project requirements analysis and UI/UX design"
      },
      {
        phase: "Backend Development",
        status: "in-progress",
        date: "2024-01-18",
        description: "API development and database setup"
      },
      {
        phase: "Frontend Development",
        status: "pending",
        date: "2024-02-01",
        description: "Mobile app development and UI implementation"
      },
      {
        phase: "Testing & Deployment",
        status: "pending",
        date: "2024-02-15",
        description: "Quality assurance and app store deployment"
      }
    ],
    team: [
      { name: "Sarah Johnson", role: "Project Manager", avatar: "👩‍💼" },
      { name: "Mike Chen", role: "Backend Developer", avatar: "👨‍💻" },
      { name: "Lisa Wang", role: "Mobile Developer", avatar: "👩‍💻" },
      { name: "David Brown", role: "UI/UX Designer", avatar: "👨‍🎨" }
    ],
    updates: [
      {
        date: "2024-01-20",
        author: "Sarah Johnson",
        message: "Backend API development is 70% complete. Payment gateway integration testing in progress."
      },
      {
        date: "2024-01-18",
        author: "Mike Chen",
        message: "Database schema finalized and initial API endpoints created."
      },
      {
        date: "2024-01-16",
        author: "David Brown",
        message: "UI/UX designs approved by client. Moving to development phase."
      }
    ]
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'status-completed'
      case 'in-progress': return 'status-progress'
      case 'pending': return 'status-planning'
      default: return 'status-default'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed': return <CheckCircle size={16} />
      case 'in-progress': return <Clock size={16} />
      case 'pending': return <AlertCircle size={16} />
      default: return <Clock size={16} />
    }
  }

  return (
    <div className="project-details">
      {/* Navigation */}
      <nav className="navbar">
        <div className="nav-container">
          <Link to="/" className="logo">RENDER SOLUTIONS</Link>
          <ul className="nav-links">
            <li><Link to="/">Home</Link></li>
            <li><Link to="/user">Dashboard</Link></li>
            <li><Link to="/submit-project">Submit Project</Link></li>
            <li><Link to="/login">Login</Link></li>
          </ul>
        </div>
      </nav>

      <div className="details-container">
        <div className="container">
          {/* Header */}
          <div className="details-header">
            <Link to="/user" className="back-button">
              <ArrowLeft size={20} />
              Back to Dashboard
            </Link>
            <div className="project-header-info">
              <h1 className="project-title">{project.title}</h1>
              <div className="project-meta">
                <span className={`project-status ${getStatusColor(project.status.toLowerCase().replace(' ', '-'))}`}>
                  {project.status}
                </span>
                <span className="project-category">{project.category}</span>
                <span className="project-priority priority-high">High Priority</span>
              </div>
            </div>
          </div>

          {/* Tabs */}
          <div className="details-tabs">
            <button 
              className={`tab-button ${activeTab === 'overview' ? 'active' : ''}`}
              onClick={() => setActiveTab('overview')}
            >
              <FileText size={20} />
              Overview
            </button>
            <button 
              className={`tab-button ${activeTab === 'progress' ? 'active' : ''}`}
              onClick={() => setActiveTab('progress')}
            >
              <Target size={20} />
              Progress
            </button>
            <button 
              className={`tab-button ${activeTab === 'team' ? 'active' : ''}`}
              onClick={() => setActiveTab('team')}
            >
              <Users size={20} />
              Team
            </button>
            <button 
              className={`tab-button ${activeTab === 'updates' ? 'active' : ''}`}
              onClick={() => setActiveTab('updates')}
            >
              <MessageCircle size={20} />
              Updates
            </button>
          </div>

          {/* Tab Content */}
          <div className="tab-content">
            {activeTab === 'overview' && (
              <div className="overview-content">
                <div className="grid grid-2">
                  <div className="main-content">
                    <div className="card">
                      <h3 className="card-title">Project Description</h3>
                      <p className="project-description">{project.description}</p>
                    </div>

                    <div className="card">
                      <h3 className="card-title">Project Goals</h3>
                      <ul className="goals-list">
                        {project.goals.map((goal, index) => (
                          <li key={index} className="goal-item">
                            <Target size={16} className="text-primary" />
                            {goal}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div className="card">
                      <h3 className="card-title">Technical Requirements</h3>
                      <ul className="requirements-list">
                        {project.requirements.map((requirement, index) => (
                          <li key={index} className="requirement-item">
                            <CheckCircle size={16} className="text-primary" />
                            {requirement}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  <div className="sidebar-content">
                    <div className="card">
                      <h3 className="card-title">Project Information</h3>
                      <div className="info-list">
                        <div className="info-item">
                          <User size={18} />
                          <div>
                            <span className="info-label">Submitted by</span>
                            <span className="info-value">{project.submittedBy}</span>
                          </div>
                        </div>
                        <div className="info-item">
                          <Calendar size={18} />
                          <div>
                            <span className="info-label">Submitted on</span>
                            <span className="info-value">{new Date(project.submittedDate).toLocaleDateString()}</span>
                          </div>
                        </div>
                        <div className="info-item">
                          <DollarSign size={18} />
                          <div>
                            <span className="info-label">Budget</span>
                            <span className="info-value">{project.budget}</span>
                          </div>
                        </div>
                        <div className="info-item">
                          <Clock size={18} />
                          <div>
                            <span className="info-label">Timeline</span>
                            <span className="info-value">{project.timeline}</span>
                          </div>
                        </div>
                        <div className="info-item">
                          <Users size={18} />
                          <div>
                            <span className="info-label">Team Size</span>
                            <span className="info-value">{project.teamSize}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="card">
                      <h3 className="card-title">Tags</h3>
                      <div className="tags-container">
                        {project.tags.map(tag => (
                          <span key={tag} className="project-tag">
                            <Tag size={12} />
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'progress' && (
              <div className="progress-content">
                <div className="card">
                  <h3 className="card-title">Project Timeline</h3>
                  <div className="timeline">
                    {project.progress.map((phase, index) => (
                      <div key={index} className={`timeline-item ${phase.status}`}>
                        <div className="timeline-marker">
                          {getStatusIcon(phase.status)}
                        </div>
                        <div className="timeline-content">
                          <h4 className="timeline-title">{phase.phase}</h4>
                          <p className="timeline-description">{phase.description}</p>
                          <span className="timeline-date">{new Date(phase.date).toLocaleDateString()}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'team' && (
              <div className="team-content">
                <div className="card">
                  <h3 className="card-title">Project Team</h3>
                  <div className="team-grid">
                    {project.team.map((member, index) => (
                      <div key={index} className="team-member">
                        <div className="member-avatar">{member.avatar}</div>
                        <div className="member-info">
                          <h4 className="member-name">{member.name}</h4>
                          <p className="member-role">{member.role}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'updates' && (
              <div className="updates-content">
                <div className="card">
                  <h3 className="card-title">Project Updates</h3>
                  <div className="updates-list">
                    {project.updates.map((update, index) => (
                      <div key={index} className="update-item">
                        <div className="update-header">
                          <span className="update-author">{update.author}</span>
                          <span className="update-date">{new Date(update.date).toLocaleDateString()}</span>
                        </div>
                        <p className="update-message">{update.message}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProjectDetails
