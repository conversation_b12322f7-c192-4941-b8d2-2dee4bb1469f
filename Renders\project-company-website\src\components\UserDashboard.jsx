import { useState, useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Plus, Search, Filter, Eye, Calendar, User, Tag, Sparkles, TrendingUp } from 'lucide-react'

const UserDashboard = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [filterCategory, setFilterCategory] = useState('all')
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    setIsLoaded(true)
  }, [])

  // Sample project data
  const projects = [
    {
      id: 1,
      title: "E-commerce Mobile App",
      description: "A modern mobile app for online shopping with AI recommendations",
      category: "Mobile Development",
      status: "In Progress",
      submittedBy: "John Doe",
      submittedDate: "2024-01-15",
      tags: ["React Native", "AI", "E-commerce"]
    },
    {
      id: 2,
      title: "Smart Home IoT System",
      description: "Integrated IoT solution for home automation and energy management",
      category: "IoT",
      status: "Completed",
      submittedBy: "<PERSON> Smith",
      submittedDate: "2024-01-10",
      tags: ["IoT", "Smart Home", "Energy"]
    },
    {
      id: 3,
      title: "AI-Powered Learning Platform",
      description: "Educational platform with personalized learning paths using machine learning",
      category: "Education",
      status: "Under Review",
      submittedBy: "Mike <PERSON>",
      submittedDate: "2024-01-20",
      tags: ["AI", "Education", "Machine Learning"]
    },
    {
      id: 4,
      title: "Blockchain Voting System",
      description: "Secure and transparent voting system using blockchain technology",
      category: "Blockchain",
      status: "In Progress",
      submittedBy: "Sarah Wilson",
      submittedDate: "2024-01-18",
      tags: ["Blockchain", "Security", "Voting"]
    },
    {
      id: 5,
      title: "Virtual Reality Training",
      description: "VR-based training modules for medical professionals",
      category: "VR/AR",
      status: "Planning",
      submittedBy: "Dr. Brown",
      submittedDate: "2024-01-22",
      tags: ["VR", "Medical", "Training"]
    },
    {
      id: 6,
      title: "Green Energy Dashboard",
      description: "Real-time monitoring dashboard for renewable energy systems",
      category: "Sustainability",
      status: "Completed",
      submittedBy: "Green Tech Co.",
      submittedDate: "2024-01-12",
      tags: ["Green Energy", "Dashboard", "Monitoring"]
    }
  ]

  const categories = ['all', 'Mobile Development', 'IoT', 'Education', 'Blockchain', 'VR/AR', 'Sustainability']

  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesCategory = filterCategory === 'all' || project.category === filterCategory
    return matchesSearch && matchesCategory
  })

  const getStatusColor = (status) => {
    switch (status) {
      case 'Completed': return 'status-completed'
      case 'In Progress': return 'status-progress'
      case 'Under Review': return 'status-review'
      case 'Planning': return 'status-planning'
      default: return 'status-default'
    }
  }

  return (
    <div className="user-dashboard">
      {/* Floating Particles */}
      <div className="floating-particles">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="particle"></div>
        ))}
      </div>

      {/* Navigation */}
      <nav className="navbar">
        <div className="nav-container">
          <Link to="/" className="logo">
            <Sparkles size={24} style={{marginRight: '0.5rem'}} />
            RENDER SOLUTIONS
          </Link>
          <ul className="nav-links">
            <li><Link to="/">Home</Link></li>
            <li><Link to="/user">Dashboard</Link></li>
            <li><Link to="/submit-project">Submit Project</Link></li>
            <li><Link to="/login">Admin</Link></li>
          </ul>
        </div>
      </nav>

      <div className="dashboard-container">
        <div className="container">
          {/* Header */}
          <div className={`dashboard-header ${isLoaded ? 'animate-in' : ''}`}>
            <h1 className="dashboard-title">
              Project <span className="text-gradient">Dashboard</span>
            </h1>
            <p className="dashboard-subtitle">Explore innovative projects and submit your own ideas</p>
            <div className="header-stats">
              <div className="stat-badge">
                <TrendingUp size={16} />
                <span>{filteredProjects.length} Projects</span>
              </div>
              <div className="stat-badge">
                <User size={16} />
                <span>Active Community</span>
              </div>
            </div>
            <Link to="/submit-project" className="btn btn-primary btn-pulse">
              <Plus size={20} />
              Submit New Project
            </Link>
          </div>

          {/* Search and Filter */}
          <div className={`dashboard-controls ${isLoaded ? 'animate-in' : ''}`}>
            <div className="search-box">
              <Search size={20} className="search-icon" />
              <input
                type="text"
                placeholder="Search projects, tags, or descriptions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="search-input"
              />
            </div>
            <div className="filter-box">
              <Filter size={20} className="filter-icon" />
              <select
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
                className="filter-select"
              >
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category === 'all' ? 'All Categories' : category}
                  </option>
                ))}
              </select>
            </div>
            <div className="results-count">
              <span>{filteredProjects.length} projects found</span>
            </div>
          </div>

          {/* Projects Grid */}
          <div className="projects-grid">
            {filteredProjects.map((project, index) => (
              <div
                key={project.id}
                className={`project-card card card-animate hover-lift ${isLoaded ? 'animate-in' : ''}`}
                style={{animationDelay: `${index * 0.1}s`}}
              >
                <div className="project-header">
                  <h3 className="project-title">{project.title}</h3>
                  <span className={`project-status ${getStatusColor(project.status)}`}>
                    {project.status}
                  </span>
                </div>
                <p className="project-description">{project.description}</p>
                <div className="project-meta">
                  <div className="meta-item">
                    <User size={16} />
                    <span>{project.submittedBy}</span>
                  </div>
                  <div className="meta-item">
                    <Calendar size={16} />
                    <span>{new Date(project.submittedDate).toLocaleDateString()}</span>
                  </div>
                </div>
                <div className="project-tags">
                  {project.tags.map(tag => (
                    <span key={tag} className="project-tag">
                      <Tag size={12} />
                      {tag}
                    </span>
                  ))}
                </div>
                <div className="project-actions">
                  <Link to={`/project/${project.id}`} className="btn btn-secondary">
                    <Eye size={16} />
                    View Details
                  </Link>
                </div>
                <div className="project-glow"></div>
              </div>
            ))}
          </div>

          {filteredProjects.length === 0 && (
            <div className="no-results">
              <div className="no-results-icon">
                <Search size={64} style={{opacity: 0.3}} />
              </div>
              <h3>No projects found</h3>
              <p>Try adjusting your search terms or filters, or submit a new project to get started!</p>
              <Link to="/submit-project" className="btn btn-primary">
                <Plus size={20} />
                Submit Your First Project
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default UserDashboard
