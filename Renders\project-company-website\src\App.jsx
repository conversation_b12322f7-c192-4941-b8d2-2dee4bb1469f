import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import HomePage from './components/HomePage'
import UserDashboard from './components/UserDashboard'
import AdminDashboard from './components/AdminDashboard'
import Login from './components/Login'
import ProjectSubmission from './components/ProjectSubmission'
import ProjectDetails from './components/ProjectDetails'
import './App.css'

function App() {
  return (
    <Router>
      <div className="App">
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/login" element={<Login />} />
          <Route path="/user" element={<UserDashboard />} />
          <Route path="/admin" element={<AdminDashboard />} />
          <Route path="/submit-project" element={<ProjectSubmission />} />
          <Route path="/project/:id" element={<ProjectDetails />} />
        </Routes>
      </div>
    </Router>
  )
}

export default App
