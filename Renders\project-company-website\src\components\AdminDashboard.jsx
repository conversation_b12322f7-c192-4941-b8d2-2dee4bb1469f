import { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { 
  Bar<PERSON>hart3, 
  Users, 
  FolderOpen, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  Edit,
  Trash2,
  Eye,
  Filter,
  Search
} from 'lucide-react'

const AdminDashboard = () => {
  const [activeTab, setActiveTab] = useState('overview')
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')

  // Sample data
  const stats = {
    totalProjects: 156,
    activeProjects: 42,
    completedProjects: 89,
    pendingReview: 25
  }

  const recentProjects = [
    {
      id: 1,
      title: "E-commerce Mobile App",
      submittedBy: "<PERSON>",
      status: "In Progress",
      priority: "High",
      submittedDate: "2024-01-15",
      lastUpdated: "2024-01-20"
    },
    {
      id: 2,
      title: "Smart Home IoT System",
      submittedBy: "<PERSON>",
      status: "Under Review",
      priority: "Medium",
      submittedDate: "2024-01-10",
      lastUpdated: "2024-01-18"
    },
    {
      id: 3,
      title: "AI Learning Platform",
      submittedBy: "<PERSON>",
      status: "Completed",
      priority: "High",
      submittedDate: "2024-01-08",
      lastUpdated: "2024-01-19"
    },
    {
      id: 4,
      title: "Blockchain Voting System",
      submittedBy: "Sarah Wilson",
      status: "Planning",
      priority: "Low",
      submittedDate: "2024-01-12",
      lastUpdated: "2024-01-17"
    }
  ]

  const users = [
    { id: 1, name: "John Doe", email: "<EMAIL>", projects: 5, joinDate: "2023-12-01" },
    { id: 2, name: "Jane Smith", email: "<EMAIL>", projects: 3, joinDate: "2023-11-15" },
    { id: 3, name: "Mike Johnson", email: "<EMAIL>", projects: 7, joinDate: "2023-10-20" },
    { id: 4, name: "Sarah Wilson", email: "<EMAIL>", projects: 2, joinDate: "2024-01-05" }
  ]

  const getStatusColor = (status) => {
    switch (status) {
      case 'Completed': return 'status-completed'
      case 'In Progress': return 'status-progress'
      case 'Under Review': return 'status-review'
      case 'Planning': return 'status-planning'
      default: return 'status-default'
    }
  }

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'High': return 'priority-high'
      case 'Medium': return 'priority-medium'
      case 'Low': return 'priority-low'
      default: return 'priority-default'
    }
  }

  const filteredProjects = recentProjects.filter(project => {
    const matchesSearch = project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.submittedBy.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || project.status === statusFilter
    return matchesSearch && matchesStatus
  })

  return (
    <div className="admin-dashboard">
      {/* Navigation */}
      <nav className="navbar">
        <div className="nav-container">
          <Link to="/" className="logo">RENDER SOLUTIONS Admin</Link>
          <ul className="nav-links">
            <li><Link to="/">Home</Link></li>
            <li><Link to="/user">User View</Link></li>
            <li><Link to="/admin">Admin Dashboard</Link></li>
          </ul>
        </div>
      </nav>

      <div className="admin-container">
        <div className="container">
          {/* Header */}
          <div className="admin-header">
            <h1 className="admin-title">Admin Dashboard</h1>
            <p className="admin-subtitle">Manage projects, users, and system overview</p>
          </div>

          {/* Stats Cards */}
          <div className="stats-grid">
            <div className="stat-card card">
              <div className="stat-icon">
                <FolderOpen size={32} className="text-primary" />
              </div>
              <div className="stat-content">
                <div className="stat-number">{stats.totalProjects}</div>
                <div className="stat-label">Total Projects</div>
              </div>
            </div>
            <div className="stat-card card">
              <div className="stat-icon">
                <Clock size={32} style={{color: '#f39c12'}} />
              </div>
              <div className="stat-content">
                <div className="stat-number">{stats.activeProjects}</div>
                <div className="stat-label">Active Projects</div>
              </div>
            </div>
            <div className="stat-card card">
              <div className="stat-icon">
                <CheckCircle size={32} style={{color: '#27ae60'}} />
              </div>
              <div className="stat-content">
                <div className="stat-number">{stats.completedProjects}</div>
                <div className="stat-label">Completed</div>
              </div>
            </div>
            <div className="stat-card card">
              <div className="stat-icon">
                <AlertCircle size={32} style={{color: '#e74c3c'}} />
              </div>
              <div className="stat-content">
                <div className="stat-number">{stats.pendingReview}</div>
                <div className="stat-label">Pending Review</div>
              </div>
            </div>
          </div>

          {/* Tabs */}
          <div className="admin-tabs">
            <button 
              className={`tab-button ${activeTab === 'overview' ? 'active' : ''}`}
              onClick={() => setActiveTab('overview')}
            >
              <BarChart3 size={20} />
              Overview
            </button>
            <button 
              className={`tab-button ${activeTab === 'projects' ? 'active' : ''}`}
              onClick={() => setActiveTab('projects')}
            >
              <FolderOpen size={20} />
              Projects
            </button>
            <button 
              className={`tab-button ${activeTab === 'users' ? 'active' : ''}`}
              onClick={() => setActiveTab('users')}
            >
              <Users size={20} />
              Users
            </button>
          </div>

          {/* Tab Content */}
          <div className="tab-content">
            {activeTab === 'overview' && (
              <div className="overview-content">
                <div className="grid grid-2">
                  <div className="card">
                    <h3 className="card-title">Recent Activity</h3>
                    <div className="activity-list">
                      <div className="activity-item">
                        <div className="activity-icon">
                          <CheckCircle size={16} style={{color: '#27ae60'}} />
                        </div>
                        <div className="activity-content">
                          <p><strong>AI Learning Platform</strong> marked as completed</p>
                          <span className="activity-time">2 hours ago</span>
                        </div>
                      </div>
                      <div className="activity-item">
                        <div className="activity-icon">
                          <Clock size={16} style={{color: '#f39c12'}} />
                        </div>
                        <div className="activity-content">
                          <p><strong>Smart Home IoT</strong> moved to review</p>
                          <span className="activity-time">4 hours ago</span>
                        </div>
                      </div>
                      <div className="activity-item">
                        <div className="activity-icon">
                          <FolderOpen size={16} className="text-primary" />
                        </div>
                        <div className="activity-content">
                          <p>New project <strong>VR Training</strong> submitted</p>
                          <span className="activity-time">1 day ago</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="card">
                    <h3 className="card-title">Quick Actions</h3>
                    <div className="quick-actions">
                      <button className="btn btn-primary mb-2">Review Pending Projects</button>
                      <button className="btn btn-secondary mb-2">Generate Report</button>
                      <button className="btn btn-secondary mb-2">Manage Users</button>
                      <button className="btn btn-secondary">System Settings</button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'projects' && (
              <div className="projects-content">
                {/* Search and Filter */}
                <div className="admin-controls">
                  <div className="search-box">
                    <Search size={20} className="search-icon" />
                    <input
                      type="text"
                      placeholder="Search projects..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="search-input"
                    />
                  </div>
                  <div className="filter-box">
                    <Filter size={20} className="filter-icon" />
                    <select
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                      className="filter-select"
                    >
                      <option value="all">All Status</option>
                      <option value="In Progress">In Progress</option>
                      <option value="Under Review">Under Review</option>
                      <option value="Completed">Completed</option>
                      <option value="Planning">Planning</option>
                    </select>
                  </div>
                </div>

                {/* Projects Table */}
                <div className="projects-table card">
                  <div className="table-header">
                    <h3>Project Management</h3>
                  </div>
                  <div className="table-content">
                    <table>
                      <thead>
                        <tr>
                          <th>Project</th>
                          <th>Submitted By</th>
                          <th>Status</th>
                          <th>Priority</th>
                          <th>Last Updated</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {filteredProjects.map(project => (
                          <tr key={project.id}>
                            <td>
                              <div className="project-info">
                                <strong>{project.title}</strong>
                              </div>
                            </td>
                            <td>{project.submittedBy}</td>
                            <td>
                              <span className={`status-badge ${getStatusColor(project.status)}`}>
                                {project.status}
                              </span>
                            </td>
                            <td>
                              <span className={`priority-badge ${getPriorityColor(project.priority)}`}>
                                {project.priority}
                              </span>
                            </td>
                            <td>{new Date(project.lastUpdated).toLocaleDateString()}</td>
                            <td>
                              <div className="action-buttons">
                                <button className="btn-icon" title="View">
                                  <Eye size={16} />
                                </button>
                                <button className="btn-icon" title="Edit">
                                  <Edit size={16} />
                                </button>
                                <button className="btn-icon btn-danger" title="Delete">
                                  <Trash2 size={16} />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'users' && (
              <div className="users-content">
                <div className="users-table card">
                  <div className="table-header">
                    <h3>User Management</h3>
                  </div>
                  <div className="table-content">
                    <table>
                      <thead>
                        <tr>
                          <th>Name</th>
                          <th>Email</th>
                          <th>Projects</th>
                          <th>Join Date</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {users.map(user => (
                          <tr key={user.id}>
                            <td><strong>{user.name}</strong></td>
                            <td>{user.email}</td>
                            <td>{user.projects}</td>
                            <td>{new Date(user.joinDate).toLocaleDateString()}</td>
                            <td>
                              <div className="action-buttons">
                                <button className="btn-icon" title="View">
                                  <Eye size={16} />
                                </button>
                                <button className="btn-icon" title="Edit">
                                  <Edit size={16} />
                                </button>
                                <button className="btn-icon btn-danger" title="Delete">
                                  <Trash2 size={16} />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default AdminDashboard
